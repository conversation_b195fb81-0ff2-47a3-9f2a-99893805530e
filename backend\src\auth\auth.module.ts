import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtService as CustomJwtService } from './jwt.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { AuthMiddleware, StrictAuthMiddleware } from './middleware/auth.middleware';
import { JwtAuthGuard, OptionalAuthGuard, LocalAuthGuard } from './guards';
import { AuthInterceptor, TokenRefreshInterceptor } from './interceptors/auth.interceptor';
import { AuthExceptionFilter } from './filters/auth-exception.filter';
import { RbacService } from './rbac/rbac.service';
import { RbacGuard } from './rbac/rbac.guard';
import { User } from '../entities/user.entity';
import { SupabaseService } from '../config/supabase.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-super-secret-jwt-key-here',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN') || '15m',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    CustomJwtService,
    JwtStrategy,
    AuthMiddleware,
    StrictAuthMiddleware,
    JwtAuthGuard,
    OptionalAuthGuard,
    LocalAuthGuard,
    AuthInterceptor,
    TokenRefreshInterceptor,
    AuthExceptionFilter,
    RbacService,
    RbacGuard,
    SupabaseService,
  ],
  exports: [
    AuthService,
    CustomJwtService,
    JwtModule,
    PassportModule,
    AuthMiddleware,
    StrictAuthMiddleware,
    JwtAuthGuard,
    OptionalAuthGuard,
    LocalAuthGuard,
    AuthInterceptor,
    TokenRefreshInterceptor,
    AuthExceptionFilter,
    RbacService,
    RbacGuard,
  ],
})
export class AuthModule {}
