import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User, UserRole } from '../entities/user.entity';
import { SupabaseService } from '../config/supabase.config';
import { LoginDto, RegisterDto } from './dto';
import { AuthResponseDto } from './dto/token.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly supabaseService: SupabaseService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { email, password, role = UserRole.STUDENT, profileData } = registerDto;

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    try {
      // Create user in Supabase Auth
      const { data: authData, error: authError } = await this.supabaseService.signUp(
        email,
        password,
        {
          role,
          ...profileData,
        },
      );

      if (authError) {
        throw new BadRequestException(`Registration failed: ${authError.message}`);
      }

      if (!authData.user) {
        throw new BadRequestException('Failed to create user account');
      }

      // Hash password for local storage
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Create user in local database
      const user = this.userRepository.create({
        id: authData.user.id,
        email,
        password_hash: passwordHash,
        role,
        profile_data: profileData || {},
      });

      const savedUser = await this.userRepository.save(user);

      // Return user data and tokens
      return {
        user: {
          id: savedUser.id,
          email: savedUser.email,
          role: savedUser.role,
          profileData: savedUser.profile_data,
        },
        tokens: {
          accessToken: authData.session?.access_token || '',
          refreshToken: authData.session?.refresh_token || '',
          tokenType: 'Bearer',
          expiresIn: authData.session?.expires_in || 3600,
        },
      };
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Registration failed. Please try again.');
    }
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const { email, password } = loginDto;

    try {
      // Authenticate with Supabase
      const { data: authData, error: authError } = await this.supabaseService.signIn(
        email,
        password,
      );

      if (authError || !authData.user) {
        throw new UnauthorizedException('Invalid email or password');
      }

      // Get user from local database
      const user = await this.userRepository.findOne({
        where: { email },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Return user data and tokens
      return {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          profileData: user.profile_data,
        },
        tokens: {
          accessToken: authData.session?.access_token || '',
          refreshToken: authData.session?.refresh_token || '',
          tokenType: 'Bearer',
          expiresIn: authData.session?.expires_in || 3600,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Login failed. Please check your credentials.');
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResponseDto> {
    try {
      // Refresh token with Supabase
      const { data: authData, error: authError } = await this.supabaseService.client.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (authError || !authData.user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Get user from local database
      const user = await this.userRepository.findOne({
        where: { id: authData.user.id },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          profileData: user.profile_data,
        },
        tokens: {
          accessToken: authData.session?.access_token || '',
          refreshToken: authData.session?.refresh_token || '',
          tokenType: 'Bearer',
          expiresIn: authData.session?.expires_in || 3600,
        },
      };
    } catch (error) {
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  async logout(): Promise<{ message: string }> {
    try {
      const { error } = await this.supabaseService.signOut();
      if (error) {
        throw new BadRequestException('Logout failed');
      }
      return { message: 'Logged out successfully' };
    } catch (error) {
      throw new BadRequestException('Logout failed');
    }
  }

  async validateUser(userId: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      return user;
    } catch (error) {
      return null;
    }
  }
}
