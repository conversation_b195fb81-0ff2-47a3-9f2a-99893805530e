exports.id = 1;
exports.ids = [1];
exports.modules = {

/***/ 2012:
/***/ (() => {

throw new Error("Module build failed: UnhandledSchemeError: Reading from \"cloudflare:sockets\" is not handled by plugins (Unhandled scheme).\nWebpack supports \"data:\" and \"file:\" URIs by default.\nYou may need an additional plugin to handle \"cloudflare:\" URIs.\n    at C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\@nestjs\\cli\\node_modules\\webpack\\lib\\NormalModule.js:984:10\n    at Hook.eval [as callAsync] (eval at create (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:6:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\tapable\\lib\\Hook.js:20:14)\n    at Object.processResource (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\@nestjs\\cli\\node_modules\\webpack\\lib\\NormalModule.js:980:8)\n    at processResource (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\loader-runner\\lib\\LoaderRunner.js:220:11)\n    at iteratePitchingLoaders (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\loader-runner\\lib\\LoaderRunner.js:171:10)\n    at runLoaders (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\loader-runner\\lib\\LoaderRunner.js:398:2)\n    at NormalModule._doBuild (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\@nestjs\\cli\\node_modules\\webpack\\lib\\NormalModule.js:965:3)\n    at NormalModule.build (C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\@nestjs\\cli\\node_modules\\webpack\\lib\\NormalModule.js:1155:15)\n    at C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\@nestjs\\cli\\node_modules\\webpack\\lib\\Compilation.js:1422:12");

/***/ })

};
;